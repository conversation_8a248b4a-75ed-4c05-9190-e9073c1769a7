<script setup>

// Options API 选项式API
/*export default {
  data() {
    return {
      message: 'Hello World'
    }
  },
  methods: {
    onClick() {
      console.log('click')
    }
  }
}*/

/*export default {
  setup() {
    const message = 'Helloworld'
    const printMessage = () => {
      console.log(message)
    }

    // 将定义的变量和函数返回出去方可使用
    return {
      message,
      printMessage
    }
  }
}*/

/*// Composition API 组合式API
import {ref} from "vue";

// 响应式对象 RefImpl
const message = ref('helloworld')
const printMessage = () => {

  // 如果需要取值还需引用value属性
  console.log(message.value)
}*/

// reactive() or ref()
// 相同点：声明响应式变量
// 不同点：reactive()只能声明引用数据类型，ref()可以声明任意数据类型（原始数据类型（number string boolean undefine null）和引用数据类型）
import {computed, onMounted, provide, reactive, ref, watch} from "vue"
import MySon from "@/components/MySon.vue";
import MySon1 from "@/components/MySon1.vue";
import axios from "axios";
import MyChild from "@/components/MyChild.vue";
import MyChild1 from "@/components/MyChild1.vue";
import MyChild2 from "@/components/MyChild2.vue";

// const count = reactive(10)
/*const count = reactive({
  count: 100
})*/

// ref()底层实现就是reactive().做了扩展
// const count = ref(10)
// RefImpl
/*const count = ref({
  value: 200
})

console.log(count)
console.log(count.value.value) // 100*/

// 计算属性。computed 某一个值是依赖于另外的值计算出来的
/*const count = ref(10)
const doubleCount = computed(() => {
  return count.value * 2
})*/

// 监听数据 watch
// const count = ref(100)

/*watch(count, (newVal, oldVal) => {
  console.log(newVal, oldVal)
},{
  // 立即执行
  immediate: true,
  deep: true
})*/

/*const changeCount = () => {
  count.value = count.value * 2
}*/

/*const user = ref({
  id: 1000,
  name: 'zhangsan'
})*/

/*watch(user, (newVal, oldVal) => {
  console.log(newVal, oldVal)
}, {
  deep: true
})*/

/*watch(() => user.value.name, (newVal, oldVal) => {
  console.log(newVal, oldVal)
}, {
  deep: true
})

const changeUser = () => {
  /!*user.value = {
    id: 2000,
    name: 'lisi'
  }*!/

  user.value.name = 'wangwu'
}*/

// 父子组件传值
// 父 -----> 子

// const count = ref(200000)

// 子-------->父
/*const getMessage = (message) => {
  console.log(message)
}*/

// Vue3中生命周期函数(钩子函数)前两个(beforeCreate() created())))去除
// 取而代之的是setup()
// Vue2其他的函数,函数名称的前面加上onXxx
// Mounted()-------->OnMounted()
// 生命周期函数使用多次
// 初始化数据

/*onMounted(() => {
  console.log('onMounted()')
})

onMounted(() => {
  console.log('onMounted()')
})

onMounted(async () => {
  /!*const res = await axios.request({
    url: '',
    method: 'GET',
    params: {}
  })*!/
})*/


/*const info = ref('helloworld')
const info1 = reactive({
  content: 'helloworld'
})

console.log(info.value)
console.log(info1.content)*/

// computed 缓存机制 只会执行一次   函数没有缓存

// watch 两个属性:immediate deep

// 父子传值
// 父 -------> 子 defineProps({})
// 子 -------> 父 defineEmits(['xxx'])

/*const count = ref(10)

const changeCount = (val) => {
  count.value = val
}*/

// provided inject
// provide('info','Helloworld')

// defineExpose() 父组件需要调用子组件的变量或函数 不能直接调用.
// 但是如果子组件将变量和函数一旦暴露出去了,那么就可以调用

onMounted(() => {
  console.log(info)

  printInfo()
})

</script>

<template>
  <div id="app">
    <!--    {{ message }}

        <br>

        <button @click="p|rintMessage">打印</button>-->

    <!--    <p>{{ doubleCount}}</p>-->

    <!--    <p>这是老值：{{ count }}</p>
        <button @click="changeCount">改变</button>-->

    <!--    <p>这是老值：{{ user.id }}-&#45;&#45;&#45;&#45;{{ user.name }}</p>
        <button @click="changeUser">改变用户</button>-->

    <!-- 使用子组件，同时传递一个实参，实参的名称为message，实参的值为Helloworld    -->
    <!--    <MySon message="Helloworld" :count="count" :user="{id:1000,name:'zhangsan'}"></MySon>-->

    <!--    -->


    <!--    <MySon1 @get-message="getMessage"></MySon1>-->

    <!--    <MySon></MySon>-->

    <!--    <MyChild :count="count" @change-count="changeCount"></MyChild>-->


    <!--    <MyChild1></MyChild1>-->

    <MyChild2 :info="info"></MyChild2>
  </div>
</template>

<style scoped>
</style>
